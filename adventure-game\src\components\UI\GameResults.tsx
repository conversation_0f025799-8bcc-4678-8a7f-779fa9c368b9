import React from 'react';
import { GameResults as GameResultsType, Achievement } from '../../game/types/GameTypes';

interface GameResultsProps {
  results: GameResultsType;
  isVisible: boolean;
  onNewGame: () => void;
  onMainMenu: () => void;
}

const GameResults: React.FC<GameResultsProps> = ({ 
  results, 
  isVisible, 
  onNewGame, 
  onMainMenu 
}) => {
  if (!isVisible) return null;

  const getImpactDescription = (impact: number): string => {
    if (impact >= 5) return 'Obrovský vliv! 🌟';
    if (impact >= 3) return 'Významný vliv! 👏';
    if (impact >= 1) return 'Mírný vliv 👍';
    if (impact >= 0.5) return 'Malý vliv 🤏';
    return 'Minimální vliv 😐';
  };

  const getImpactColor = (impact: number): string => {
    if (impact >= 5) return '#f39c12';
    if (impact >= 3) return '#27ae60';
    if (impact >= 1) return '#3498db';
    if (impact >= 0.5) return '#95a5a6';
    return '#7f8c8d';
  };

  const getAchievementIcon = (type: string): string => {
    switch (type) {
      case 'positive': return '🏆';
      case 'negative': return '💀';
      case 'ironic': return '😅';
      default: return '🎖️';
    }
  };

  const getWinnerDescription = (): { winner: string; description: string; color: string } => {
    const { democracy, opposition, apathy } = results.societyResults;
    
    if (democracy > opposition && democracy > apathy) {
      return {
        winner: 'Demokracie',
        description: 'Demokratické síly zvítězily ve volbách!',
        color: '#27ae60'
      };
    } else if (opposition > democracy && opposition > apathy) {
      return {
        winner: 'Opozice',
        description: 'Opozice převzala moc.',
        color: '#e74c3c'
      };
    } else {
      return {
        winner: 'Apatie',
        description: 'Lidé ztratili zájem o politiku.',
        color: '#95a5a6'
      };
    }
  };

  const winner = getWinnerDescription();

  return (
    <div className="results-overlay">
      <div className="results-container">
        <div className="results-header">
          <h1>🗳️ Výsledky voleb</h1>
          <div className="winner-announcement" style={{ color: winner.color }}>
            <h2>{winner.winner}</h2>
            <p>{winner.description}</p>
          </div>
        </div>

        <div className="results-content">
          <div className="results-section">
            <h3>📊 Finální výsledky společnosti</h3>
            <div className="society-results">
              <div className="result-bar">
                <span className="result-label">🟢 Demokracie</span>
                <div className="result-progress">
                  <div 
                    className="result-fill democracy"
                    style={{ width: `${results.societyResults.democracy}%` }}
                  />
                  <span className="result-value">
                    {results.societyResults.democracy.toFixed(1)}%
                  </span>
                </div>
              </div>
              
              <div className="result-bar">
                <span className="result-label">🔴 Opozice</span>
                <div className="result-progress">
                  <div 
                    className="result-fill opposition"
                    style={{ width: `${results.societyResults.opposition}%` }}
                  />
                  <span className="result-value">
                    {results.societyResults.opposition.toFixed(1)}%
                  </span>
                </div>
              </div>
              
              <div className="result-bar">
                <span className="result-label">⚪ Apatie</span>
                <div className="result-progress">
                  <div 
                    className="result-fill apathy"
                    style={{ width: `${results.societyResults.apathy}%` }}
                  />
                  <span className="result-value">
                    {results.societyResults.apathy.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="results-section">
            <h3>🎯 Váš vliv</h3>
            <div className="player-impact">
              <div 
                className="impact-circle"
                style={{ borderColor: getImpactColor(results.playerImpact) }}
              >
                <span className="impact-value">
                  {results.playerImpact.toFixed(1)}%
                </span>
              </div>
              <div className="impact-description">
                {getImpactDescription(results.playerImpact)}
              </div>
            </div>
          </div>

          <div className="results-section">
            <h3>👥 Vaše bublina vlivu</h3>
            <div className="influence-summary">
              <div className="influence-stat">
                <span className="influence-icon">🟢</span>
                <span className="influence-count">{results.playerInfluence.democracyInfluence}</span>
                <span className="influence-label">pro demokracii</span>
              </div>
              <div className="influence-stat">
                <span className="influence-icon">🔴</span>
                <span className="influence-count">{results.playerInfluence.oppositionInfluence}</span>
                <span className="influence-label">pro opozici</span>
              </div>
              <div className="influence-stat">
                <span className="influence-icon">⚪</span>
                <span className="influence-count">{results.playerInfluence.apathyInfluence}</span>
                <span className="influence-label">apatických</span>
              </div>
            </div>
          </div>

          <div className="results-section">
            <h3>📈 Vaše finální statistiky</h3>
            <div className="final-stats">
              <div className="final-stat">
                <span className="stat-name">Realita:</span>
                <span className="stat-value">{Math.round(results.playerFinalStats.realita)}</span>
              </div>
              <div className="final-stat">
                <span className="stat-name">Percepce:</span>
                <span className="stat-value">{Math.round(results.playerFinalStats.percepce)}</span>
              </div>
              <div className="final-stat">
                <span className="stat-name">Vliv:</span>
                <span className="stat-value">{Math.round(results.playerFinalStats.vliv)}</span>
              </div>
            </div>
          </div>

          {results.achievements.length > 0 && (
            <div className="results-section">
              <h3>🏆 Odemčené achievementy</h3>
              <div className="achievements-list">
                {results.achievements.map(achievement => (
                  <div key={achievement.id} className="achievement-item">
                    <span className="achievement-icon">
                      {getAchievementIcon(achievement.type)}
                    </span>
                    <div className="achievement-info">
                      <div className="achievement-name">{achievement.name}</div>
                      <div className="achievement-description">{achievement.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="results-actions">
          <button className="action-button primary" onClick={onNewGame}>
            Nová hra
          </button>
          <button className="action-button secondary" onClick={onMainMenu}>
            Hlavní menu
          </button>
        </div>
      </div>
    </div>
  );
};

export default GameResults;
