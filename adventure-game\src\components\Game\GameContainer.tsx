import React, { useEffect, useState } from 'react';
import { GameManager } from '../../game/managers/GameManager';
import type { GameConfig } from '../../game/types';

interface GameContainerProps {
  width?: number;
  height?: number;
  debug?: boolean;
}

const GameContainer: React.FC<GameContainerProps> = ({
  width = 800,
  height = 600,
  debug = false
}) => {
  const [gameManager] = useState(() => GameManager.getInstance());
  const [isGameInitialized, setIsGameInitialized] = useState(false);
  const [gameError, setGameError] = useState<string | null>(null);

  useEffect(() => {
    if (isGameInitialized) {
      return;
    }

    const initializeGame = async () => {
      try {
        // Konfigurace hry
        const gameConfig: GameConfig = {
          width,
          height,
          backgroundColor: '#2c3e50',
          debug
        };

        // Inicializace hry (bez Phaser pro tuto politickou simulaci)
        await gameManager.initializePhaserGame(gameConfig);

        setIsGameInitialized(true);
        console.log('Politická simulace byla úspěšně inicializována');

      } catch (error) {
        console.error('Chyba při inicializaci hry:', error);
        setGameError('Nepodařilo se inicializovat hru');
      }
    };

    initializeGame();

    // Cleanup funkce
    return () => {
      if (isGameInitialized) {
        gameManager.destroy();
        setIsGameInitialized(false);
      }
    };
  }, [gameManager, width, height, debug, isGameInitialized]);

  if (gameError) {
    return (
      <div className="game-error">
        <h3>Chyba hry</h3>
        <p>{gameError}</p>
        <button onClick={() => window.location.reload()}>
          Obnovit stránku
        </button>
      </div>
    );
  }

  return (
    <div className="game-container-wrapper">
      <div
        className="game-container political-simulation"
        style={{
          width: `${width}px`,
          height: `${height}px`,
          border: '2px solid #34495e',
          borderRadius: '8px',
          overflow: 'hidden',
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#2c3e50'
        }}
      >
        {!isGameInitialized && (
          <div className="game-loading">
            <p>Načítání politické simulace...</p>
          </div>
        )}
        {isGameInitialized && (
          <div className="simulation-info">
            <h2>🗳️ Bublina vlivu</h2>
            <p>Politická simulace běží na pozadí</p>
            <p>Události se zobrazují v překryvných oknech</p>
            <div className="simulation-status">
              <span className="status-indicator">●</span>
              <span>Simulace aktivní</span>
            </div>
          </div>
        )}
      </div>

      {debug && (
        <div className="debug-info">
          <h4>Debug informace</h4>
          <p>Rozlišení: {width}x{height}</p>
          <p>Stav: {isGameInitialized ? 'Inicializováno' : 'Načítání'}</p>
          <p>Typ: Politická simulace</p>
        </div>
      )}
    </div>
  );
};

export default GameContainer;
