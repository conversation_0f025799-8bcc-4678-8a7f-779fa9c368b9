Herní mechanika: <PERSON><PERSON><PERSON> vlivu + <PERSON><PERSON><PERSON> společnosti
1️⃣ Základní ukazatele hráče

Hráč má tři hlavní bar<PERSON> (statistiky), k<PERSON><PERSON> se mění podle jeho rozhodnutí:

<PERSON>ita (R) → <PERSON><PERSON><PERSON>, nakolik hráč chápe skutečný stav věcí (0–100).

<PERSON><PERSON><PERSON><PERSON> (P) → jak si hrá<PERSON> my<PERSON>l<PERSON>, že se má ČR (0–100, ale často se rozchází s realitou).

Vliv (V) → kolik lidí okolo hráče se inspiruje jeho chováním (0–100).

👉 Např.: Hr<PERSON><PERSON> věř<PERSON> ho<PERSON> → Realita klesá, <PERSON><PERSON><PERSON><PERSON> jde do prdele, ale Vliv může stoupat (protože sdílí virální píčoviny).

2️⃣ Bublina vlivu (NPC okruh)

Hráč má kolem sebe okruh postav (NPC): rod<PERSON>, k<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>v<PERSON>, hospods<PERSON><PERSON> štamgasti.
Každý NPC má dvě skryté statistiky:

Náchylnost k dezinformacím (NDez, 0–100)

Skeptické myšlení (Skept, 0–100)

Podle rozhodnutí hráče:

Pokud hráč šíří dezinfa → NPC s vysokým NDez se přidávají na stranu opozice.

Pokud hráč argumentuje fakty → NPC s vyšším Skept se přidávají k demokracii.

Pokud hráč mlčí/ignoruje → NPC se posunují do apatie.

👉 Na konci se vyhodnotí, kolik NPC kolem sebe hráč strhnul na jakou stranu.

3️⃣ Simulace společnosti (globální model)

Na pozadí běží simulace celé společnosti:

Demokracie (%) – defaultně např. 55 %

Opozice (%) – defaultně 30 %

Apatie (%) – defaultně 15 %

Každé hráčovo rozhodnutí má malý posun (např. ±0,1 %).
Ale velký posun udělají i globální eventy (fake news kampaň, ekonomická zpráva, skandál politika).

👉 Kombinace hráčova chování + náhodné/skriptované eventy určí finální rozložení hlasů.

4️⃣ Herní smyčka

Hráč dostane událost (FB post, hospodský kec, rodinná debata).

Hráč zvolí reakci (věřit, ověřit, ignorovat, argumentovat).

Změní se:

jeho R, P, V hodnoty,

nálady v bublině vlivu (NPC),

a drobný posun v simulaci společnosti.

Hráč vidí okamžitý efekt (např. „Strejda Pepa se přiklání k opozici“ nebo „Tvůj kolega začal pochybovat o dezinformacích“).

5️⃣ Finální vyhodnocení (Den voleb)

Na konci hry se sečte:

Tvoje bublina vlivu: kolik NPC jsi dostal na demokracii / opozici / apatii.

Celková společnost: jak dopadly simulované volby (např. Demokracie 52 %, Opozice 37 %, Apatie 11 %).

Tvoje role: hra ukáže, o kolik procent jsi pohnul výsledkem.

👉 Díky tomu hráč cítí, že není spasitel, ale jeho rozhodnutí fakt něco změnilo.

6️⃣ Bonusové mechaniky

Algoritmus sociálních sítí: čím víc hráč sdílí blbosti, tím víc mu jich hra servíruje.

Emoční ukazatel: některé volby jsou rychlý a emotivní (hněv, strach), jiné pomalejší, ale realitě věrnější.

Falešný „achievementy“:

„Top dezinformátor – 1000 sdílení hovadin“

„Rodinný diplomat – přesvědčil jsi babičku“

„Kecal v hospodě – 0 % reálný dopad“ 😁

7️⃣ Technicky (pro programování v Cursoru)

Můžeš to udělat jednoduše jako JSON strom rozhodnutí:

{
  "event": "Rodinný oběd - Strýc Pepa: 'Za Babiše bylo líp!'",
  "choices": [
    {
      "text": "Souhlasit a přisadit",
      "effect": { "R": -10, "P": -20, "V": +5, "opposition": +0.5, "npc": "pepa:opposition+20" }
    },
    {
      "text": "Argumentovat fakty",
      "effect": { "R": +15, "P": +5, "V": +2, "democracy": +0.5, "npc": "pepa:democracy+30" }
    },
    {
      "text": "Mlčet a jíst knedlíky",
      "effect": { "R": 0, "P": -5, "V": -2, "apathy": +0.5, "npc": "pepa:apathy+20" }
    }
  ]
}