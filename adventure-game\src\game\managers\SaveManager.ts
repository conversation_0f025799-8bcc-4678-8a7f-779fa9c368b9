import type { SaveData } from '../types';

export class SaveManager {
  private readonly SAVE_KEY_PREFIX = 'adventure_game_save_';
  private readonly MAX_SAVE_SLOTS = 10;

  public saveGame(slotName: string, saveData: SaveData): boolean {
    try {
      const saveKey = this.SAVE_KEY_PREFIX + slotName;
      const serializedData = JSON.stringify(saveData);
      localStorage.setItem(saveKey, serializedData);
      console.log(`Hra uložena do slotu: ${slotName}`);
      return true;
    } catch (error) {
      console.error('Chyba při ukládání hry:', error);
      return false;
    }
  }

  public loadGame(slotName: string): SaveData | null {
    try {
      const saveKey = this.SAVE_KEY_PREFIX + slotName;
      const serializedData = localStorage.getItem(saveKey);
      
      if (!serializedData) {
        console.warn(`Uložená hra nebyla nalezena ve slotu: ${slotName}`);
        return null;
      }

      const saveData: SaveData = JSON.parse(serializedData);
      console.log(`Hra načtena ze slotu: ${slotName}`);
      return saveData;
    } catch (error) {
      console.error('Chyba při načítání hry:', error);
      return null;
    }
  }

  public deleteSave(slotName: string): boolean {
    try {
      const saveKey = this.SAVE_KEY_PREFIX + slotName;
      localStorage.removeItem(saveKey);
      console.log(`Uložená hra smazána ze slotu: ${slotName}`);
      return true;
    } catch (error) {
      console.error('Chyba při mazání uložené hry:', error);
      return false;
    }
  }

  public getSaveSlots(): string[] {
    const saveSlots: string[] = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.SAVE_KEY_PREFIX)) {
        const slotName = key.replace(this.SAVE_KEY_PREFIX, '');
        saveSlots.push(slotName);
      }
    }
    
    return saveSlots.sort();
  }

  public getSaveInfo(slotName: string): { timestamp: number; version: string } | null {
    try {
      const saveKey = this.SAVE_KEY_PREFIX + slotName;
      const serializedData = localStorage.getItem(saveKey);
      
      if (!serializedData) {
        return null;
      }

      const saveData: SaveData = JSON.parse(serializedData);
      return {
        timestamp: saveData.timestamp,
        version: saveData.version
      };
    } catch (error) {
      console.error('Chyba při získávání informací o uložené hře:', error);
      return null;
    }
  }

  public hasSave(slotName: string): boolean {
    const saveKey = this.SAVE_KEY_PREFIX + slotName;
    return localStorage.getItem(saveKey) !== null;
  }

  public getMaxSaveSlots(): number {
    return this.MAX_SAVE_SLOTS;
  }

  public exportSave(slotName: string): string | null {
    try {
      const saveKey = this.SAVE_KEY_PREFIX + slotName;
      const serializedData = localStorage.getItem(saveKey);
      
      if (!serializedData) {
        console.warn(`Uložená hra nebyla nalezena ve slotu: ${slotName}`);
        return null;
      }

      // Vytvoření base64 kódovaného exportu
      const exportData = btoa(serializedData);
      console.log(`Hra exportována ze slotu: ${slotName}`);
      return exportData;
    } catch (error) {
      console.error('Chyba při exportu hry:', error);
      return null;
    }
  }

  public importSave(slotName: string, exportData: string): boolean {
    try {
      // Dekódování base64 dat
      const serializedData = atob(exportData);
      
      // Validace dat
      const saveData: SaveData = JSON.parse(serializedData);
      if (!saveData.gameState || !saveData.timestamp || !saveData.version) {
        throw new Error('Neplatná struktura uložených dat');
      }

      const saveKey = this.SAVE_KEY_PREFIX + slotName;
      localStorage.setItem(saveKey, serializedData);
      console.log(`Hra importována do slotu: ${slotName}`);
      return true;
    } catch (error) {
      console.error('Chyba při importu hry:', error);
      return false;
    }
  }

  public clearAllSaves(): boolean {
    try {
      const saveSlots = this.getSaveSlots();
      saveSlots.forEach(slot => {
        this.deleteSave(slot);
      });
      console.log('Všechny uložené hry byly smazány');
      return true;
    } catch (error) {
      console.error('Chyba při mazání všech uložených her:', error);
      return false;
    }
  }
}
