// import * as Phaser from 'phaser'; // Nepoužíváme Phaser pro politickou simulaci
import type {
  GameState,
  Player,
  GameConfig,
  SaveData,
  PlayerStats,
  GameEvent,
  EventChoice,
  ChoiceEffects,
  GameResults,
  Achievement
} from '../types';
import { PoliticalSimulationManager } from './PoliticalSimulationManager';
import { EventManager } from './EventManager';
import { SaveManager } from './SaveManager';

export class GameManager {
  private static instance: GameManager;
  private gameState: GameState;
  private politicalSimulation: PoliticalSimulationManager;
  private eventManager: EventManager;
  private saveManager: SaveManager;
  private gameInitialized: boolean = false;
  private initialSocietyStats: any;

  private constructor() {
    this.politicalSimulation = new PoliticalSimulationManager();
    this.eventManager = new EventManager();
    this.saveManager = new SaveManager();
    this.initializeGameState();
  }

  public static getInstance(): GameManager {
    if (!GameManager.instance) {
      GameManager.instance = new GameManager();
    }
    return GameManager.instance;
  }

  private initializeGameState(): void {
    // Inicializace výchozího stavu hry
    const defaultPlayer: Player = {
      id: 'player1',
      name: 'Hráč',
      stats: {
        realita: 50,    // Střední úroveň chápání reality
        percepce: 50,   // Střední percepce stavu ČR
        vliv: 20        // Nízký počáteční vliv
      },
      currentEvent: null,
      eventsCompleted: []
    };

    const npcs = this.politicalSimulation.initializeNPCs();
    const societyStats = this.politicalSimulation.getSocietyStats();
    this.initialSocietyStats = { ...societyStats };

    this.gameState = {
      player: defaultPlayer,
      npcs: npcs,
      societyStats: societyStats,
      currentEvent: null,
      eventHistory: [],
      achievements: this.eventManager.getAllAchievements(),
      gameFlags: {},
      gameVariables: {},
      dayCount: 1,
      maxDays: 30  // 30 dní do voleb
    };
  }

  public async initializePhaserGame(config: GameConfig): Promise<void> {
    console.log('Inicializace politické simulace s konfigurací:', config);

    this.gameInitialized = true;

    // Spustí první událost po krátké pauze
    setTimeout(() => {
      this.startNextEvent();
    }, 2000);
  }

  public getGameState(): GameState {
    return this.gameState;
  }

  public getPlayer(): Player {
    return this.gameState.player;
  }

  public getCurrentEvent(): GameEvent | null {
    return this.gameState.currentEvent;
  }

  public startNextEvent(): void {
    if (this.gameState.dayCount > this.gameState.maxDays) {
      this.endGame();
      return;
    }

    const nextEvent = this.eventManager.getRandomEvent(this.gameState.eventHistory);
    if (nextEvent) {
      this.gameState.currentEvent = nextEvent;
      this.gameState.player.currentEvent = nextEvent.id;
    } else {
      // Pokud nejsou další události, posun den
      this.advanceDay();
    }
  }

  public makeChoice(choiceId: string): string[] {
    const currentEvent = this.gameState.currentEvent;
    if (!currentEvent) return [];

    const choice = currentEvent.choices.find(c => c.id === choiceId);
    if (!choice) return [];

    const messages = this.applyChoiceEffects(choice.effects);

    // Označit událost jako dokončenou
    this.gameState.eventHistory.push(currentEvent.id);
    this.gameState.player.eventsCompleted.push(currentEvent.id);
    this.gameState.currentEvent = null;
    this.gameState.player.currentEvent = null;

    // Odemknout achievementy
    if (choice.effects.achievements) {
      choice.effects.achievements.forEach(achievementId => {
        const achievement = this.eventManager.unlockAchievement(achievementId);
        if (achievement) {
          this.gameState.achievements[achievementId] = achievement;
          messages.push(`🏆 Odemčen achievement: ${achievement.name}`);
        }
      });
    }

    return messages;
  }

  private applyChoiceEffects(effects: ChoiceEffects): string[] {
    const messages: string[] = [];

    // Aplikuj změny statistik hráče
    if (effects.playerStats) {
      Object.entries(effects.playerStats).forEach(([stat, change]) => {
        if (change !== undefined) {
          const currentValue = (this.gameState.player.stats as any)[stat];
          const newValue = Math.max(0, Math.min(100, currentValue + change));
          (this.gameState.player.stats as any)[stat] = newValue;

          if (Math.abs(change) >= 5) {
            const direction = change > 0 ? '↗️' : '↘️';
            messages.push(`${direction} ${stat}: ${change > 0 ? '+' : ''}${change}`);
          }
        }
      });
    }

    // Aplikuj změny společnosti
    if (effects.societyStats) {
      this.politicalSimulation.applySocietyChange(effects.societyStats);
      this.gameState.societyStats = this.politicalSimulation.getSocietyStats();
    }

    // Aplikuj změny NPC
    if (effects.npcEffects && effects.npcEffects.length > 0) {
      const npcMessages = this.politicalSimulation.applyNPCEffects(effects.npcEffects);
      messages.push(...npcMessages);
      this.gameState.npcs = this.politicalSimulation.getNPCs();
    }

    return messages;
  }

  private advanceDay(): void {
    this.gameState.dayCount++;

    // Možnost spuštění globálních událostí
    if (Math.random() < 0.3) { // 30% šance na globální událost
      const eventTypes = ['fake_news_campaign', 'economic_news', 'political_scandal', 'social_media_trend'];
      const randomEvent = eventTypes[Math.floor(Math.random() * eventTypes.length)] as any;
      this.politicalSimulation.triggerGlobalEvent(randomEvent);
      this.gameState.societyStats = this.politicalSimulation.getSocietyStats();
    }

    // Spustit další událost
    setTimeout(() => this.startNextEvent(), 1000);
  }

  public getSaveManager(): SaveManager {
    return this.saveManager;
  }

  private endGame(): void {
    // Simulace voleb
    const finalSocietyStats = this.politicalSimulation.simulateElectionDay();
    const playerImpact = this.politicalSimulation.calculatePlayerImpact(
      this.initialSocietyStats,
      this.gameState.player.stats
    );

    const results: GameResults = {
      playerInfluence: {
        npcResults: this.politicalSimulation.getInfluenceBubbleResults(),
        totalInfluenced: Object.keys(this.gameState.npcs).length,
        democracyInfluence: 0,
        oppositionInfluence: 0,
        apathyInfluence: 0
      },
      societyResults: finalSocietyStats,
      playerFinalStats: this.gameState.player.stats,
      achievements: Object.values(this.gameState.achievements).filter(a => a.unlocked),
      playerImpact: playerImpact
    };

    // Spočítej vliv na NPC
    Object.values(this.gameState.npcs).forEach(npc => {
      switch (npc.currentStance) {
        case 'democracy':
          results.playerInfluence.democracyInfluence++;
          break;
        case 'opposition':
          results.playerInfluence.oppositionInfluence++;
          break;
        case 'apathy':
          results.playerInfluence.apathyInfluence++;
          break;
      }
    });

    this.gameState.gameVariables['gameResults'] = results;
    this.gameState.gameFlags['gameEnded'] = true;
  }

  public getGameResults(): GameResults | null {
    return this.gameState.gameVariables['gameResults'] || null;
  }

  public isGameEnded(): boolean {
    return this.gameState.gameFlags['gameEnded'] || false;
  }

  public getDaysRemaining(): number {
    return Math.max(0, this.gameState.maxDays - this.gameState.dayCount + 1);
  }

  public saveGame(slotName: string): boolean {
    const saveData: SaveData = {
      gameState: this.gameState,
      timestamp: Date.now(),
      version: '2.0.0'
    };
    return this.saveManager.saveGame(slotName, saveData);
  }

  public loadGame(slotName: string): boolean {
    const saveData = this.saveManager.loadGame(slotName);
    if (saveData) {
      this.gameState = saveData.gameState;
      // Obnovit managery
      this.politicalSimulation = new PoliticalSimulationManager();
      this.eventManager = new EventManager();
      return true;
    }
    return false;
  }

  public destroy(): void {
    this.gameInitialized = false;
    console.log('Politická simulace ukončena');
  }
}
