import React, { useState, useEffect } from 'react';

interface Message {
  id: string;
  text: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: number;
}

interface MessageDisplayProps {
  messages: string[];
  onMessagesCleared: () => void;
}

const MessageDisplay: React.FC<MessageDisplayProps> = ({ messages, onMessagesCleared }) => {
  const [displayMessages, setDisplayMessages] = useState<Message[]>([]);

  useEffect(() => {
    if (messages.length > 0) {
      const newMessages: Message[] = messages.map((text, index) => ({
        id: `${Date.now()}-${index}`,
        text,
        type: getMessageType(text),
        timestamp: Date.now()
      }));

      setDisplayMessages(prev => [...prev, ...newMessages]);
      onMessagesCleared();

      // Automaticky odstraň zprávy po 5 sekundách
      const timer = setTimeout(() => {
        setDisplayMessages(prev => 
          prev.filter(msg => Date.now() - msg.timestamp < 5000)
        );
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [messages, onMessagesCleared]);

  const getMessageType = (text: string): 'info' | 'success' | 'warning' | 'error' => {
    if (text.includes('↗️') || text.includes('🏆') || text.includes('🟢')) {
      return 'success';
    }
    if (text.includes('↘️') || text.includes('🔴')) {
      return 'warning';
    }
    if (text.includes('❌') || text.includes('💀')) {
      return 'error';
    }
    return 'info';
  };

  const getMessageIcon = (type: string): string => {
    switch (type) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      default: return 'ℹ️';
    }
  };

  const removeMessage = (messageId: string) => {
    setDisplayMessages(prev => prev.filter(msg => msg.id !== messageId));
  };

  if (displayMessages.length === 0) {
    return null;
  }

  return (
    <div className="message-display">
      {displayMessages.map(message => (
        <div 
          key={message.id} 
          className={`message-item ${message.type}`}
          onClick={() => removeMessage(message.id)}
        >
          <span className="message-icon">
            {getMessageIcon(message.type)}
          </span>
          <span className="message-text">
            {message.text}
          </span>
          <button 
            className="message-close"
            onClick={(e) => {
              e.stopPropagation();
              removeMessage(message.id);
            }}
          >
            ×
          </button>
        </div>
      ))}
    </div>
  );
};

export default MessageDisplay;
