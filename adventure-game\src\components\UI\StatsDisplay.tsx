import React from 'react';
import type { PlayerStats, SocietyStats, NPC } from '../../game/types';

interface StatsDisplayProps {
  playerStats: PlayerStats;
  societyStats: SocietyStats;
  npcs: { [key: string]: NPC };
  daysRemaining: number;
}

const StatsDisplay: React.FC<StatsDisplayProps> = ({ 
  playerStats, 
  societyStats, 
  npcs, 
  daysRemaining 
}) => {
  const getStatColor = (value: number): string => {
    if (value >= 70) return '#27ae60'; // zelená
    if (value >= 40) return '#f39c12'; // oran<PERSON><PERSON>
    return '#e74c3c'; // červená
  };

  const getStatBar = (value: number, label: string): JSX.Element => (
    <div className="stat-bar">
      <div className="stat-label">{label}</div>
      <div className="stat-progress">
        <div 
          className="stat-fill"
          style={{ 
            width: `${value}%`,
            backgroundColor: getStatColor(value)
          }}
        />
        <span className="stat-value">{Math.round(value)}</span>
      </div>
    </div>
  );

  const getNPCStanceIcon = (stance: string): string => {
    switch (stance) {
      case 'democracy': return '🟢';
      case 'opposition': return '🔴';
      case 'apathy': return '⚪';
      default: return '❓';
    }
  };

  const getNPCStanceName = (stance: string): string => {
    switch (stance) {
      case 'democracy': return 'Demokracie';
      case 'opposition': return 'Opozice';
      case 'apathy': return 'Apatie';
      default: return 'Neznámé';
    }
  };

  const getRelationshipIcon = (relationship: string): string => {
    switch (relationship) {
      case 'family': return '👨‍👩‍👧‍👦';
      case 'friend': return '👥';
      case 'colleague': return '💼';
      case 'neighbor': return '🏠';
      case 'acquaintance': return '🤝';
      default: return '👤';
    }
  };

  return (
    <div className="stats-display">
      <div className="stats-section">
        <h3>📊 Vaše statistiky</h3>
        <div className="player-stats">
          {getStatBar(playerStats.realita, 'Realita (R)')}
          {getStatBar(playerStats.percepce, 'Percepce (P)')}
          {getStatBar(playerStats.vliv, 'Vliv (V)')}
        </div>
        
        <div className="stat-descriptions">
          <div className="stat-desc">
            <strong>Realita:</strong> Míra chápání skutečného stavu věcí
          </div>
          <div className="stat-desc">
            <strong>Percepce:</strong> Jak si myslíte, že se má ČR
          </div>
          <div className="stat-desc">
            <strong>Vliv:</strong> Kolik lidí se inspiruje vaším chováním
          </div>
        </div>
      </div>

      <div className="stats-section">
        <h3>🏛️ Stav společnosti</h3>
        <div className="society-stats">
          {getStatBar(societyStats.democracy, 'Demokracie')}
          {getStatBar(societyStats.opposition, 'Opozice')}
          {getStatBar(societyStats.apathy, 'Apatie')}
        </div>
      </div>

      <div className="stats-section">
        <h3>👥 Vaše bublina vlivu</h3>
        <div className="npc-list">
          {Object.values(npcs).map(npc => (
            <div key={npc.id} className="npc-item">
              <div className="npc-info">
                <span className="npc-relationship">
                  {getRelationshipIcon(npc.relationship)}
                </span>
                <span className="npc-name">{npc.name}</span>
              </div>
              <div className="npc-stance">
                <span className="stance-icon">
                  {getNPCStanceIcon(npc.currentStance)}
                </span>
                <span className="stance-name">
                  {getNPCStanceName(npc.currentStance)}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="stats-section">
        <h3>📅 Čas do voleb</h3>
        <div className="days-remaining">
          <div className="days-number">{daysRemaining}</div>
          <div className="days-label">dní</div>
        </div>
      </div>
    </div>
  );
};

export default StatsDisplay;
