// Hern<PERSON> typy pro politickou simulaci "Bublina vlivu"

export interface GameConfig {
  width: number;
  height: number;
  backgroundColor: string;
  debug: boolean;
}

// Statistiky hráče
export interface PlayerStats {
  realita: number;      // R: m<PERSON>ra ch<PERSON> s<PERSON> (0-100)
  percepce: number;     // P: jak si hr<PERSON>, že se má ČR (0-100)
  vliv: number;         // V: kolik lidí se inspiruje hráčovým chováním (0-100)
}

export interface Player {
  id: string;
  name: string;
  stats: PlayerStats;
  currentEvent: string | null;
  eventsCompleted: string[];
}

// NPC systém - lidé v bublině vlivu hráče
export interface NPC {
  id: string;
  name: string;
  relationship: NPCRelationship;
  nachylnostDezinfo: number;    // NDez: náchylnost k dezinformacím (0-100)
  skeptickeMysleni: number;     // Skept: skeptic<PERSON><PERSON> (0-100)
  currentStance: PoliticalStance;
  stanceHistory: PoliticalStance[];
}

export enum NPCRelationship {
  FAMILY = 'family',
  FRIEND = 'friend',
  COLLEAGUE = 'colleague',
  NEIGHBOR = 'neighbor',
  ACQUAINTANCE = 'acquaintance'
}

export enum PoliticalStance {
  DEMOCRACY = 'democracy',
  OPPOSITION = 'opposition',
  APATHY = 'apathy'
}

// Simulace společnosti
export interface SocietyStats {
  democracy: number;    // % podpory demokracie
  opposition: number;   // % podpory opozice
  apathy: number;       // % apatických lidí
}

// Herní události
export interface GameEvent {
  id: string;
  title: string;
  description: string;
  context: EventContext;
  choices: EventChoice[];
  requiredEvents?: string[];  // události, které musí být dokončeny před touto
  weight: number;            // pravděpodobnost výskytu
}

export enum EventContext {
  FAMILY = 'family',
  WORK = 'work',
  SOCIAL_MEDIA = 'social_media',
  PUB = 'pub',
  NEWS = 'news',
  FRIENDS = 'friends'
}

export interface EventChoice {
  id: string;
  text: string;
  description?: string;
  effects: ChoiceEffects;
  emotionalType: EmotionalType;
  timeToDecide?: number;  // čas na rozhodnutí v sekundách
}

export enum EmotionalType {
  QUICK_EMOTIONAL = 'quick_emotional',  // rychlé emotivní rozhodnutí
  THOUGHTFUL = 'thoughtful',           // promyšlené rozhodnutí
  NEUTRAL = 'neutral'                  // neutrální
}

// Efekty rozhodnutí
export interface ChoiceEffects {
  playerStats: Partial<PlayerStats>;  // změny R, P, V
  societyStats: Partial<SocietyStats>; // změny v simulaci společnosti
  npcEffects: NPCEffect[];             // změny u konkrétních NPC
  globalEvents?: GlobalEventTrigger[]; // spuštění globálních událostí
  achievements?: string[];             // odemčené achievementy
}

export interface NPCEffect {
  npcId: string;
  stanceChange: {
    democracy?: number;
    opposition?: number;
    apathy?: number;
  };
}

export interface GlobalEventTrigger {
  eventType: GlobalEventType;
  probability: number;  // pravděpodobnost spuštění (0-1)
  delay?: number;       // zpoždění v herních kolech
}

export enum GlobalEventType {
  FAKE_NEWS_CAMPAIGN = 'fake_news_campaign',
  ECONOMIC_NEWS = 'economic_news',
  POLITICAL_SCANDAL = 'political_scandal',
  SOCIAL_MEDIA_TREND = 'social_media_trend'
}

// Achievementy
export interface Achievement {
  id: string;
  name: string;
  description: string;
  type: AchievementType;
  unlocked: boolean;
  unlockedAt?: number;
}

export enum AchievementType {
  POSITIVE = 'positive',
  NEGATIVE = 'negative',
  IRONIC = 'ironic'
}

// Herní stav
export interface GameState {
  player: Player;
  npcs: { [key: string]: NPC };
  societyStats: SocietyStats;
  currentEvent: GameEvent | null;
  eventHistory: string[];
  achievements: { [key: string]: Achievement };
  gameFlags: { [key: string]: boolean };
  gameVariables: { [key: string]: any };
  dayCount: number;
  maxDays: number;
}

// Výsledky hry
export interface GameResults {
  playerInfluence: {
    npcResults: { [key: string]: PoliticalStance };
    totalInfluenced: number;
    democracyInfluence: number;
    oppositionInfluence: number;
    apathyInfluence: number;
  };
  societyResults: SocietyStats;
  playerFinalStats: PlayerStats;
  achievements: Achievement[];
  playerImpact: number; // o kolik % hráč ovlivnil výsledek
}

export interface SaveData {
  gameState: GameState;
  timestamp: number;
  version: string;
}
