import {
  GameEvent,
  EventChoice,
  ChoiceEffects,
  EventContext,
  EmotionalType,
  PlayerStats,
  Achievement,
  AchievementType
} from '../types/GameTypes';

export class EventManager {
  private events: { [key: string]: GameEvent } = {};
  private achievements: { [key: string]: Achievement } = {};
  private completedEvents: string[] = [];

  constructor() {
    this.initializeEvents();
    this.initializeAchievements();
  }

  private initializeEvents(): void {
    this.events = {
      rodinny_obed: {
        id: 'rodinny_obed',
        title: '<PERSON><PERSON><PERSON> oběd',
        description: 'Strejda Pepa: "Za Babiše bylo líp! Teď je všechno drahý a politici kradou!"',
        context: EventContext.FAMILY,
        choices: [
          {
            id: 'souhlasit',
            text: 'Souhlasit a přisadit',
            description: '<PERSON>ych<PERSON> souhlasíte a přidáte vlastní stížnosti',
            emotionalType: EmotionalType.QUICK_EMOTIONAL,
            effects: {
              playerStats: { realita: -10, percepce: -20, vliv: 5 },
              societyStats: { opposition: 0.5 },
              npcEffects: [
                { npcId: 'strejda_pepa', stanceChange: { opposition: 20 } }
              ]
            }
          },
          {
            id: 'argumentovat',
            text: 'Argumentovat fakty',
            description: 'Klidně vysvětlíte skutečné ekonomické údaje',
            emotionalType: EmotionalType.THOUGHTFUL,
            timeToDecide: 10,
            effects: {
              playerStats: { realita: 15, percepce: 5, vliv: 2 },
              societyStats: { democracy: 0.5 },
              npcEffects: [
                { npcId: 'strejda_pepa', stanceChange: { democracy: 30 } }
              ]
            }
          },
          {
            id: 'mlcet',
            text: 'Mlčet a jíst knedlíky',
            description: 'Raději se soustředíte na jídlo',
            emotionalType: EmotionalType.NEUTRAL,
            effects: {
              playerStats: { realita: 0, percepce: -5, vliv: -2 },
              societyStats: { apathy: 0.5 },
              npcEffects: [
                { npcId: 'strejda_pepa', stanceChange: { apathy: 20 } }
              ]
            }
          }
        ],
        weight: 1.0
      },

      facebook_post: {
        id: 'facebook_post',
        title: 'Facebook post',
        description: 'Vidíte sdílený příspěvek: "ŠOKUJÍCÍ! Vláda skrývá pravdu o cenách energií! Sdílej než to smažou!"',
        context: EventContext.SOCIAL_MEDIA,
        choices: [
          {
            id: 'sdilet',
            text: 'Okamžitě sdílet',
            description: 'Rychle kliknete na sdílet',
            emotionalType: EmotionalType.QUICK_EMOTIONAL,
            timeToDecide: 5,
            effects: {
              playerStats: { realita: -15, percepce: -10, vliv: 10 },
              societyStats: { opposition: 1.0 },
              npcEffects: [
                { npcId: 'kamarad_tom', stanceChange: { opposition: 15 } },
                { npcId: 'sousedka_jana', stanceChange: { opposition: 10 } }
              ],
              achievements: ['top_dezinformator']
            }
          },
          {
            id: 'overit',
            text: 'Ověřit informace',
            description: 'Najdete si oficiální zdroje a ověříte fakta',
            emotionalType: EmotionalType.THOUGHTFUL,
            timeToDecide: 15,
            effects: {
              playerStats: { realita: 20, percepce: 10, vliv: -5 },
              societyStats: { democracy: 0.3 },
              npcEffects: [
                { npcId: 'kolega_martin', stanceChange: { democracy: 25 } }
              ],
              achievements: ['fact_checker']
            }
          },
          {
            id: 'ignorovat',
            text: 'Ignorovat',
            description: 'Prostě scrollujete dál',
            emotionalType: EmotionalType.NEUTRAL,
            effects: {
              playerStats: { realita: 0, percepce: 0, vliv: 0 },
              societyStats: { apathy: 0.2 },
              npcEffects: []
            }
          }
        ],
        weight: 1.0
      },

      hospoda_debata: {
        id: 'hospoda_debata',
        title: 'Debata v hospodě',
        description: 'Hospodský Franta: "Ty volby jsou stejně zmanipulovaný! Všechno je domluvený!"',
        context: EventContext.PUB,
        choices: [
          {
            id: 'souhlasit_konspiraci',
            text: 'Souhlasit s konspiracemi',
            description: 'Přikyvujete a přidáte vlastní teorie',
            emotionalType: EmotionalType.QUICK_EMOTIONAL,
            effects: {
              playerStats: { realita: -20, percepce: -15, vliv: 8 },
              societyStats: { opposition: 0.8, apathy: 0.2 },
              npcEffects: [
                { npcId: 'hospodsky_franta', stanceChange: { opposition: 25 } }
              ]
            }
          },
          {
            id: 'obhajovat_demokracii',
            text: 'Obhajovat demokratický systém',
            description: 'Vysvětlujete, jak volby skutečně fungují',
            emotionalType: EmotionalType.THOUGHTFUL,
            timeToDecide: 12,
            effects: {
              playerStats: { realita: 18, percepce: 8, vliv: 3 },
              societyStats: { democracy: 0.6 },
              npcEffects: [
                { npcId: 'hospodsky_franta', stanceChange: { democracy: 20 } }
              ],
              achievements: ['rodinny_diplomat']
            }
          },
          {
            id: 'zmenit_tema',
            text: 'Změnit téma na fotbal',
            description: 'Raději mluvíte o sportu',
            emotionalType: EmotionalType.NEUTRAL,
            effects: {
              playerStats: { realita: 0, percepce: -2, vliv: -1 },
              societyStats: { apathy: 0.3 },
              npcEffects: [
                { npcId: 'hospodsky_franta', stanceChange: { apathy: 15 } }
              ],
              achievements: ['kecal_v_hospode']
            }
          }
        ],
        weight: 0.8
      },

      prace_konverzace: {
        id: 'prace_konverzace',
        title: 'Konverzace v práci',
        description: 'Kolega Martin: "Četl jsem zajímavou analýzu o ekonomických datech. Chceš se podívat?"',
        context: EventContext.WORK,
        choices: [
          {
            id: 'zajima_me',
            text: 'Ano, zajímá mě to',
            description: 'Projevíte zájem o faktické informace',
            emotionalType: EmotionalType.THOUGHTFUL,
            effects: {
              playerStats: { realita: 12, percepce: 8, vliv: 1 },
              societyStats: { democracy: 0.3 },
              npcEffects: [
                { npcId: 'kolega_martin', stanceChange: { democracy: 20 } }
              ]
            }
          },
          {
            id: 'nezajima_me',
            text: 'Ne, nemám čas',
            description: 'Odmítnete s výmluvou',
            emotionalType: EmotionalType.QUICK_EMOTIONAL,
            effects: {
              playerStats: { realita: -5, percepce: -3, vliv: -2 },
              societyStats: { apathy: 0.2 },
              npcEffects: [
                { npcId: 'kolega_martin', stanceChange: { apathy: 10 } }
              ]
            }
          }
        ],
        weight: 0.6
      }
    };
  }

  private initializeAchievements(): void {
    this.achievements = {
      top_dezinformator: {
        id: 'top_dezinformator',
        name: 'Top dezinformátor',
        description: '1000 sdílení hovadin',
        type: AchievementType.NEGATIVE,
        unlocked: false
      },
      fact_checker: {
        id: 'fact_checker',
        name: 'Ověřovač faktů',
        description: 'Ověřil jsi 10 informací před sdílením',
        type: AchievementType.POSITIVE,
        unlocked: false
      },
      rodinny_diplomat: {
        id: 'rodinny_diplomat',
        name: 'Rodinný diplomat',
        description: 'Přesvědčil jsi příbuzného',
        type: AchievementType.POSITIVE,
        unlocked: false
      },
      kecal_v_hospode: {
        id: 'kecal_v_hospode',
        name: 'Kecal v hospodě',
        description: '0% reálný dopad 😁',
        type: AchievementType.IRONIC,
        unlocked: false
      }
    };
  }

  public getRandomEvent(completedEvents: string[]): GameEvent | null {
    const availableEvents = Object.values(this.events).filter(event => {
      // Kontrola, zda událost nebyla dokončena
      if (completedEvents.includes(event.id)) return false;
      
      // Kontrola požadovaných událostí
      if (event.requiredEvents) {
        return event.requiredEvents.every(reqEvent => completedEvents.includes(reqEvent));
      }
      
      return true;
    });

    if (availableEvents.length === 0) return null;

    // Výběr podle váhy
    const totalWeight = availableEvents.reduce((sum, event) => sum + event.weight, 0);
    let random = Math.random() * totalWeight;
    
    for (const event of availableEvents) {
      random -= event.weight;
      if (random <= 0) {
        return event;
      }
    }

    return availableEvents[0]; // fallback
  }

  public getEvent(eventId: string): GameEvent | null {
    return this.events[eventId] || null;
  }

  public getAllAchievements(): { [key: string]: Achievement } {
    return { ...this.achievements };
  }

  public unlockAchievement(achievementId: string): Achievement | null {
    const achievement = this.achievements[achievementId];
    if (achievement && !achievement.unlocked) {
      achievement.unlocked = true;
      achievement.unlockedAt = Date.now();
      return achievement;
    }
    return null;
  }

  public getContextDescription(context: EventContext): string {
    const descriptions = {
      family: '👨‍👩‍👧‍👦 Rodina',
      work: '💼 Práce',
      social_media: '📱 Sociální sítě',
      pub: '🍺 Hospoda',
      news: '📺 Zprávy',
      friends: '👥 Přátelé'
    };
    return descriptions[context] || context;
  }
}
