import React, { useState, useEffect } from 'react';
import GameContainer from './components/Game/GameContainer';
import MainMenu from './components/UI/MainMenu';
import EventDisplay from './components/UI/EventDisplay';
import StatsDisplay from './components/UI/StatsDisplay';
import GameResults from './components/UI/GameResults';
import MessageDisplay from './components/UI/MessageDisplay';
import { GameManager } from './game/managers/GameManager';
import type { GameResults as GameResultsType } from './game/types';
import './App.css';

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isEventVisible, setIsEventVisible] = useState(false);
  const [isStatsVisible, setIsStatsVisible] = useState(true);
  const [gameResults, setGameResults] = useState<GameResultsType | null>(null);
  const [messages, setMessages] = useState<string[]>([]);
  const [gameManager] = useState(() => GameManager.getInstance());

  useEffect(() => {
    // Nastavení klávesových zkratek
    const handleKeyPress = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'Escape':
          setIsMenuOpen(!isMenuOpen);
          break;
        case 's':
        case 'S':
          if (!isMenuOpen) {
            setIsStatsVisible(!isStatsVisible);
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isMenuOpen, isStatsVisible]);

  useEffect(() => {
    // Kontrola herního stavu
    const checkGameState = () => {
      const currentEvent = gameManager.getCurrentEvent();
      setIsEventVisible(!!currentEvent);

      const results = gameManager.getGameResults();
      if (results && !gameResults) {
        setGameResults(results);
      }
    };

    const interval = setInterval(checkGameState, 500);
    return () => clearInterval(interval);
  }, [gameManager, gameResults]);

  const handleNewGame = () => {
    setGameResults(null);
    window.location.reload();
  };

  const handleLoadGame = () => {
    setGameResults(null);
    console.log('Hra načtena');
  };

  const handleSaveGame = () => {
    console.log('Hra uložena');
  };

  const handleSettings = () => {
    console.log('Otevření nastavení');
  };

  const handleQuit = () => {
    if (confirm('Opravdu chcete ukončit hru?')) {
      window.close();
    }
  };

  const handleChoiceMade = (newMessages: string[]) => {
    setMessages(newMessages);
    // Spustit další událost po krátké pauze
    setTimeout(() => {
      gameManager.startNextEvent();
    }, 1500);
  };

  const handleMessagesCleared = () => {
    setMessages([]);
  };

  const gameState = gameManager.getGameState();

  return (
    <div className="app">
      <header className="app-header">
        <h1>🗳️ Bublina vlivu</h1>
        <div className="header-controls">
          <button onClick={() => setIsStatsVisible(!isStatsVisible)}>
            Statistiky (S)
          </button>
          <button onClick={() => setIsMenuOpen(true)}>
            Menu (ESC)
          </button>
        </div>
      </header>

      <main className="app-main">
        <div className="game-layout">
          <div className="game-area">
            <GameContainer
              width={800}
              height={600}
              debug={false}
            />
          </div>

          {isStatsVisible && (
            <div className="stats-area">
              <StatsDisplay
                playerStats={gameState.player.stats}
                societyStats={gameState.societyStats}
                npcs={gameState.npcs}
                daysRemaining={gameManager.getDaysRemaining()}
              />
            </div>
          )}
        </div>
      </main>

      <MainMenu
        isOpen={isMenuOpen}
        onClose={() => setIsMenuOpen(false)}
        onNewGame={handleNewGame}
        onLoadGame={handleLoadGame}
        onSaveGame={handleSaveGame}
        onSettings={handleSettings}
        onQuit={handleQuit}
      />

      <EventDisplay
        isVisible={isEventVisible}
        onChoiceMade={handleChoiceMade}
      />

      <GameResults
        results={gameResults!}
        isVisible={!!gameResults}
        onNewGame={handleNewGame}
        onMainMenu={() => {
          setGameResults(null);
          setIsMenuOpen(true);
        }}
      />

      <MessageDisplay
        messages={messages}
        onMessagesCleared={handleMessagesCleared}
      />

      <footer className="app-footer">
        <p>Použijte ESC pro menu, S pro statistiky | Den {gameState.dayCount}/{gameState.maxDays}</p>
      </footer>
    </div>
  );
}

export default App;
