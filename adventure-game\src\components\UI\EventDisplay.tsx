import React, { useState, useEffect } from 'react';
import type { GameEvent, EventChoice, EmotionalType } from '../../game/types';
import { GameManager } from '../../game/managers/GameManager';

interface EventDisplayProps {
  isVisible: boolean;
  onChoiceMade: (messages: string[]) => void;
}

const EventDisplay: React.FC<EventDisplayProps> = ({ isVisible, onChoiceMade }) => {
  const [currentEvent, setCurrentEvent] = useState<GameEvent | null>(null);
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [selectedChoice, setSelectedChoice] = useState<EventChoice | null>(null);
  const gameManager = GameManager.getInstance();

  useEffect(() => {
    if (isVisible) {
      const event = gameManager.getCurrentEvent();
      setCurrentEvent(event);
      setSelectedChoice(null);
    }
  }, [isVisible, gameManager]);

  useEffect(() => {
    if (selectedChoice && selectedChoice.timeToDecide) {
      setTimeRemaining(selectedChoice.timeToDecide);
      
      const timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev === null || prev <= 1) {
            clearInterval(timer);
            // Automaticky vybrat volbu po vypršení času
            handleChoiceClick(selectedChoice);
            return null;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [selectedChoice]);

  const handleChoiceClick = (choice: EventChoice) => {
    if (choice.timeToDecide && !selectedChoice) {
      // Pokud má volba časový limit, nejdřív ji vyber
      setSelectedChoice(choice);
      return;
    }

    // Aplikuj volbu
    const messages = gameManager.makeChoice(choice.id);
    onChoiceMade(messages);
    setCurrentEvent(null);
    setSelectedChoice(null);
    setTimeRemaining(null);
  };

  const handleConfirmChoice = () => {
    if (selectedChoice) {
      const messages = gameManager.makeChoice(selectedChoice.id);
      onChoiceMade(messages);
      setCurrentEvent(null);
      setSelectedChoice(null);
      setTimeRemaining(null);
    }
  };

  const handleCancelChoice = () => {
    setSelectedChoice(null);
    setTimeRemaining(null);
  };

  const getEmotionalTypeIcon = (type: EmotionalType): string => {
    switch (type) {
      case 'quick_emotional':
        return '⚡';
      case 'thoughtful':
        return '🤔';
      case 'neutral':
        return '😐';
      default:
        return '';
    }
  };

  const getEmotionalTypeDescription = (type: EmotionalType): string => {
    switch (type) {
      case 'quick_emotional':
        return 'Rychlé emotivní rozhodnutí';
      case 'thoughtful':
        return 'Promyšlené rozhodnutí';
      case 'neutral':
        return 'Neutrální volba';
      default:
        return '';
    }
  };

  const getContextIcon = (context: string): string => {
    const icons = {
      family: '👨‍👩‍👧‍👦',
      work: '💼',
      social_media: '📱',
      pub: '🍺',
      news: '📺',
      friends: '👥'
    };
    return icons[context as keyof typeof icons] || '📍';
  };

  if (!isVisible || !currentEvent) {
    return null;
  }

  return (
    <div className="event-overlay">
      <div className="event-container">
        <div className="event-header">
          <div className="event-context">
            {getContextIcon(currentEvent.context)} {currentEvent.title}
          </div>
        </div>

        <div className="event-content">
          <div className="event-description">
            {currentEvent.description}
          </div>

          {!selectedChoice && (
            <div className="event-choices">
              <h3>Jak zareagujete?</h3>
              {currentEvent.choices.map((choice) => (
                <button
                  key={choice.id}
                  className={`choice-button ${choice.emotionalType}`}
                  onClick={() => handleChoiceClick(choice)}
                  title={choice.description}
                >
                  <div className="choice-content">
                    <div className="choice-text">
                      {getEmotionalTypeIcon(choice.emotionalType)} {choice.text}
                    </div>
                    {choice.timeToDecide && (
                      <div className="choice-timer-info">
                        ⏱️ {choice.timeToDecide}s na rozhodnutí
                      </div>
                    )}
                  </div>
                  <div className="choice-type">
                    {getEmotionalTypeDescription(choice.emotionalType)}
                  </div>
                </button>
              ))}
            </div>
          )}

          {selectedChoice && (
            <div className="choice-confirmation">
              <h3>Potvrzení volby</h3>
              <div className="selected-choice">
                <strong>{selectedChoice.text}</strong>
                <p>{selectedChoice.description}</p>
              </div>
              
              {timeRemaining !== null && (
                <div className="countdown">
                  <div className="countdown-text">
                    Automatické potvrzení za: <strong>{timeRemaining}s</strong>
                  </div>
                  <div className="countdown-bar">
                    <div 
                      className="countdown-progress"
                      style={{ 
                        width: `${(timeRemaining / (selectedChoice.timeToDecide || 1)) * 100}%` 
                      }}
                    />
                  </div>
                </div>
              )}

              <div className="confirmation-buttons">
                <button 
                  className="confirm-button"
                  onClick={handleConfirmChoice}
                >
                  Potvrdit
                </button>
                <button 
                  className="cancel-button"
                  onClick={handleCancelChoice}
                >
                  Zpět
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EventDisplay;
