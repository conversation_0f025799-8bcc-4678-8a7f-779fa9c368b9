import { 
  SocietyStats, 
  NPC, 
  PoliticalStance, 
  NPCEffect, 
  GlobalEventType,
  PlayerStats 
} from '../types/GameTypes';

export class PoliticalSimulationManager {
  private societyStats: SocietyStats;
  private npcs: { [key: string]: NPC };
  private globalEventQueue: Array<{ type: GlobalEventType; delay: number }> = [];

  constructor() {
    // Výchozí rozložení společnosti
    this.societyStats = {
      democracy: 55,
      opposition: 30,
      apathy: 15
    };
    this.npcs = {};
  }

  public initializeNPCs(): { [key: string]: NPC } {
    const npcs: { [key: string]: NPC } = {
      strejda_pepa: {
        id: 'strejda_pepa',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        relationship: 'family',
        nachylnostDezinfo: 75,
        skeptickeMysleni: 25,
        currentStance: 'opposition',
        stanceHistory: ['opposition']
      },
      babicka: {
        id: 'babicka',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        relationship: 'family',
        nachylnostDezinfo: 60,
        skeptickeMysleni: 40,
        currentStance: 'apathy',
        stanceHistory: ['apathy']
      },
      kolega_martin: {
        id: 'kolega_martin',
        name: '<PERSON>lega <PERSON>',
        relationship: 'colleague',
        nachylnostDezinfo: 30,
        skeptickeMysleni: 70,
        currentStance: 'democracy',
        stanceHistory: ['democracy']
      },
      kamarad_tom: {
        id: 'kamarad_tom',
        name: 'Kamarád Tom',
        relationship: 'friend',
        nachylnostDezinfo: 45,
        skeptickeMysleni: 55,
        currentStance: 'democracy',
        stanceHistory: ['democracy']
      },
      hospodsky_franta: {
        id: 'hospodsky_franta',
        name: 'Hospodský Franta',
        relationship: 'acquaintance',
        nachylnostDezinfo: 80,
        skeptickeMysleni: 20,
        currentStance: 'opposition',
        stanceHistory: ['opposition']
      },
      sousedka_jana: {
        id: 'sousedka_jana',
        name: 'Sousedka Jana',
        relationship: 'neighbor',
        nachylnostDezinfo: 40,
        skeptickeMysleni: 60,
        currentStance: 'apathy',
        stanceHistory: ['apathy']
      }
    };

    this.npcs = npcs;
    return npcs;
  }

  public getSocietyStats(): SocietyStats {
    return { ...this.societyStats };
  }

  public getNPCs(): { [key: string]: NPC } {
    return { ...this.npcs };
  }

  public applySocietyChange(changes: Partial<SocietyStats>): void {
    if (changes.democracy) {
      this.societyStats.democracy = Math.max(0, Math.min(100, 
        this.societyStats.democracy + changes.democracy));
    }
    if (changes.opposition) {
      this.societyStats.opposition = Math.max(0, Math.min(100, 
        this.societyStats.opposition + changes.opposition));
    }
    if (changes.apathy) {
      this.societyStats.apathy = Math.max(0, Math.min(100, 
        this.societyStats.apathy + changes.apathy));
    }

    // Normalizace na 100%
    this.normalizeSocietyStats();
  }

  private normalizeSocietyStats(): void {
    const total = this.societyStats.democracy + this.societyStats.opposition + this.societyStats.apathy;
    if (total !== 100) {
      const factor = 100 / total;
      this.societyStats.democracy *= factor;
      this.societyStats.opposition *= factor;
      this.societyStats.apathy *= factor;
    }
  }

  public applyNPCEffects(effects: NPCEffect[]): string[] {
    const messages: string[] = [];

    effects.forEach(effect => {
      const npc = this.npcs[effect.npcId];
      if (!npc) return;

      const oldStance = npc.currentStance;
      let newStance = oldStance;
      let maxChange = 0;

      // Najdi největší změnu
      Object.entries(effect.stanceChange).forEach(([stance, change]) => {
        if (Math.abs(change || 0) > Math.abs(maxChange)) {
          maxChange = change || 0;
          newStance = stance as PoliticalStance;
        }
      });

      // Aplikuj změnu podle charakteristik NPC
      const effectiveChange = this.calculateNPCStanceChange(npc, newStance, maxChange);
      
      if (effectiveChange > 20) { // Významná změna
        npc.currentStance = newStance;
        npc.stanceHistory.push(newStance);
        messages.push(this.generateNPCChangeMessage(npc, oldStance, newStance));
      }
    });

    return messages;
  }

  private calculateNPCStanceChange(npc: NPC, targetStance: PoliticalStance, baseChange: number): number {
    let modifier = 1;

    switch (targetStance) {
      case 'opposition':
        // Vyšší náchylnost k dezinfo = větší šance na přechod k opozici
        modifier = npc.nachylnostDezinfo / 100;
        break;
      case 'democracy':
        // Vyšší skeptické myšlení = větší šance na přechod k demokracii
        modifier = npc.skeptickeMysleni / 100;
        break;
      case 'apathy':
        // Apatie je neutrální
        modifier = 0.5;
        break;
    }

    return Math.abs(baseChange) * modifier;
  }

  private generateNPCChangeMessage(npc: NPC, oldStance: PoliticalStance, newStance: PoliticalStance): string {
    const stanceNames = {
      democracy: 'demokracii',
      opposition: 'opozici',
      apathy: 'apatii'
    };

    return `${npc.name} se přiklání k ${stanceNames[newStance]} (dříve: ${stanceNames[oldStance]})`;
  }

  public triggerGlobalEvent(eventType: GlobalEventType): Partial<SocietyStats> {
    let changes: Partial<SocietyStats> = {};

    switch (eventType) {
      case 'fake_news_campaign':
        changes = { democracy: -2, opposition: +1.5, apathy: +0.5 };
        break;
      case 'economic_news':
        // Náhodně pozitivní nebo negativní
        const isPositive = Math.random() > 0.5;
        changes = isPositive 
          ? { democracy: +1, opposition: -0.5, apathy: -0.5 }
          : { democracy: -1, opposition: +0.5, apathy: +0.5 };
        break;
      case 'political_scandal':
        changes = { democracy: -1.5, opposition: +0.5, apathy: +1 };
        break;
      case 'social_media_trend':
        changes = { democracy: -0.5, opposition: +1, apathy: -0.5 };
        break;
    }

    this.applySocietyChange(changes);
    return changes;
  }

  public calculatePlayerImpact(initialStats: SocietyStats, playerStats: PlayerStats): number {
    const totalChange = Math.abs(this.societyStats.democracy - initialStats.democracy) +
                       Math.abs(this.societyStats.opposition - initialStats.opposition) +
                       Math.abs(this.societyStats.apathy - initialStats.apathy);

    // Vliv hráče je úměrný jeho statistice "Vliv" a celkové změně
    const playerImpactFactor = playerStats.vliv / 100;
    return Math.min(totalChange * playerImpactFactor, 10); // Max 10% vliv
  }

  public getInfluenceBubbleResults(): { [key: string]: PoliticalStance } {
    const results: { [key: string]: PoliticalStance } = {};
    Object.values(this.npcs).forEach(npc => {
      results[npc.id] = npc.currentStance;
    });
    return results;
  }

  public simulateElectionDay(): SocietyStats {
    // Přidej trochu náhodnosti pro finální výsledek
    const randomFactor = 0.02; // ±2%
    
    const finalStats = {
      democracy: this.societyStats.democracy + (Math.random() - 0.5) * randomFactor * 100,
      opposition: this.societyStats.opposition + (Math.random() - 0.5) * randomFactor * 100,
      apathy: this.societyStats.apathy + (Math.random() - 0.5) * randomFactor * 100
    };

    // Normalizace
    const total = finalStats.democracy + finalStats.opposition + finalStats.apathy;
    const factor = 100 / total;
    
    return {
      democracy: Math.round(finalStats.democracy * factor * 10) / 10,
      opposition: Math.round(finalStats.opposition * factor * 10) / 10,
      apathy: Math.round(finalStats.apathy * factor * 10) / 10
    };
  }
}
